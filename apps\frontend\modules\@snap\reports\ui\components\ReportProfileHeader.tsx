import React, { useState, useEffect } from 'react';
import { Avatar, ChamferBox, CustomLabel, Text } from '@snap/design-system';
import { getInitials } from '../../helpers';
import { REPORT_CONSTANTS } from '../../config/constants';
import { isValidUrl } from '../strategies/helpers.strategy';
import { cn } from './utils';
import { useReportMetadata, useReportType, useProfileImage } from '../../context/ReportContext';
import { ReportsCustomLabel } from './ReportsCustomLabel';
import { getAgeFromBirthDate } from '~/helpers';
import { FaUsers } from 'react-icons/fa6';
import { TbBuildings } from 'react-icons/tb';

const ReportProfileHeader: React.FC = () => {
  const metadata = useReportMetadata();
  const reportType = useReportType();
  const image = useProfileImage();
  const [isAvatarLoading, setIsAvatarLoading] = useState(false);
  const profileName = (metadata[REPORT_CONSTANTS.new_report.subject_name] as string);
  const initials = getInitials(profileName);
  const ageFromMeta = metadata[REPORT_CONSTANTS.new_report.subject_age] as string | number | undefined;
  const idade = typeof ageFromMeta === 'number' ? ageFromMeta : getAgeFromBirthDate(metadata[REPORT_CONSTANTS.new_report.subject_age] as string);
  const data_fundacao = new Date(ageFromMeta ? ageFromMeta as string : '').toLocaleDateString('pt-BR');
  const sexo = (metadata[REPORT_CONSTANTS.new_report.subject_sex] as string);
  const nomeMae = (metadata[REPORT_CONSTANTS.new_report.subject_mother_name] as string);
  const companyCount = metadata[REPORT_CONSTANTS.new_report.subject_company_count];
  const personCount = metadata[REPORT_CONSTANTS.new_report.subject_person_count];
  const reportSearchArgs = metadata[REPORT_CONSTANTS.new_report.report_search_args];
  // Helper function to determine if subject is a company or person
  const isCompany = () => {
    // CPF is always a person
    if (reportType === REPORT_CONSTANTS.types.cpf) {
      return false;
    }

    // CNPJ is always a company
    if (reportType === REPORT_CONSTANTS.types.cnpj) {
      return true;
    }

    // For telefone/email, check if it has personal attributes
    if (reportType === REPORT_CONSTANTS.types.telefone || reportType === REPORT_CONSTANTS.types.email) {
      // If has sex or mother name, it's a person
      const hasSex = sexo && sexo.trim();
      const hasMotherName = nomeMae && nomeMae.trim();

      if (hasSex || hasMotherName) {
        return false;
      } else if (ageFromMeta != null && ageFromMeta !== "") {
        return true;
      }
    }

    return false;
  };

  const communProps = [
    { label: 'PESSOAS ENCONTRADAS', value: personCount },
    { label: 'EMPRESAS ENCONTRADAS', value: companyCount },
  ];

  const ageLabel = isCompany() ? 'DATA DE FUNDAÇÃO:' : 'IDADE:';
  const ageValue = isCompany() ? data_fundacao : idade;

  const personProps = [
    { label: ageLabel, value: ageValue },
    { label: 'SEXO:', value: sexo },
    { label: 'NOME DA MÃE:', value: nomeMae },
  ];
  const companyProps = [
    { label: 'STATUS NA RECEITA:', value: 'ATIVO' }, // TODO - mockado
    { label: ageLabel, value: ageValue },
  ];

  const getSearchArguments = (): Array<{ key: string; value: string }> => {
    if (!reportSearchArgs || typeof reportSearchArgs !== 'object') {
      return [];
    }
    const searchArgsRecord = reportSearchArgs as Record<string, unknown>;
    return Object.entries(searchArgsRecord)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .map(([key, value]) => ({
        key: key.toUpperCase(),
        value: String(value)
      }));
  };
  const isValidValue = (val: any) => val !== undefined && val !== null && val !== '' && val !== 0;

  const getReportProps = () => {
    const props = communProps;
    if (['cpf', 'telefone', 'email'].includes(reportType)) {
      props.push(...personProps);
    }

    if (reportType === 'cnpj') {
      props.push(
        ...companyProps,
      );
    }

    return props.filter(prop => isValidValue(prop.value));
  };

  const renderProps = () => (
    getReportProps().map((prop) => (
      <div key={prop.label}>
        <ReportsCustomLabel label={prop.label} />
        <Text variant="body-md">{prop.value as string}</Text>
      </div>
    ))
  );

  useEffect(() => {
    setIsAvatarLoading(true);
    if (!image) {
      setIsAvatarLoading(false);
      return;
    }
    const img = new Image();
    img.src = image;
    img.onload = () => setIsAvatarLoading(false);
    img.onerror = () => setIsAvatarLoading(false);
    const timer = setTimeout(() => setIsAvatarLoading(false), 5000);
    return () => clearTimeout(timer);
  }, [image]);

  const isMultipleSubjects = profileName === REPORT_CONSTANTS.multiplos_registros_encontrados;
  const multipleIcon = personCount ? <FaUsers className="size-16" /> : <TbBuildings className="size-16" />;

  return (
    <ChamferBox corner="bottomRight" className="!border-border-alt w-full rounded-xl [--border:var(--border-alt)] p-4">
      <div className="flex gap-6">
        <div className="relative h-46 w-46 aspect-square">
          {!isMultipleSubjects ? (
            <>
              {isAvatarLoading && (
                <div className="absolute inset-0 z-10 flex items-center justify-center h-full">
                  <div className="animate-spin h-20 w-20 border-4 border-primary border-t-transparent rounded-full"></div>
                </div>
              )}
              <div className={`${isAvatarLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-1000 ease-in-out aspect-square`}>
                <Avatar
                  size="22"
                  className={cn(
                    "border-8 border-border w-full h-full",
                    "[&_img]:h-full [&_img]:w-auto [&_img]:object-cover",
                    "[&_span]:h-40 [&_span]:w-40 [&_span]:text-6xl"
                  )}
                  src={isValidUrl(image || '') ? image : undefined}
                  fallback={initials}
                  textAlign="left"
                />
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-full w-full">
              <div className="border-8 border-border rounded-full p-6 flex items-center justify-center h-46 w-46">
                {multipleIcon}
              </div>
            </div>
          )}
        </div>


        <div className="flex-2/4 space-y-8">
          <h1 className="text-3xl font-bold">{profileName}</h1>
          <div className="grid grid-cols-2 gap-4">{renderProps()}</div>
        </div>
        <div className="flex-1/5 pl-4 space-y-4">
          <h2 className="text-lg font-mono">ENTRADAS</h2>
          {getSearchArguments().map(({ key, value }) => (
            <div key={key}>
              <ReportsCustomLabel label={key} colorClass="bg-primary" />
              <Text>{value}</Text>
            </div>
          ))}
        </div>
      </div>
    </ChamferBox>
  );
};

export default ReportProfileHeader;